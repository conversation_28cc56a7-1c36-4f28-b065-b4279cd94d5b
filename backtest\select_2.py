import pandas as pd
import ast
from collections import Counter
import matplotlib.pyplot as plt

window_size = 23

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

def load_data(file_path):
    """加载彩票数据"""
    df = pd.read_csv(file_path)
    # 解析numbers列
    df['numbers_list'] = df['numbers'].apply(lambda x: [int(num) for num in ast.literal_eval(x)])
    return df

def get_top_numbers(history_data):
    """获取历史数据中出现频次最高的数字"""
    all_numbers = []
    for numbers in history_data:
        all_numbers.extend(numbers)
    
    counter = Counter(all_numbers)
    
    if not counter:
        return [], False
    
    max_count = max(counter.values())
    top_numbers = [num for num, count in counter.items() if count == max_count]
    
    is_valid = len(top_numbers) == 2
    
    return sorted(top_numbers), is_valid

def check_prediction(prediction_numbers, actual_numbers):
    """检查预测是否命中"""
    return any(num in actual_numbers for num in prediction_numbers)

def backtest_with_visualization():
    """执行回测并生成可视化数据"""
    df = load_data('happy8_data.csv')
    
    total_profit = 0
    consecutive_losses = 0
    max_consecutive_losses = 0
    
    results = []
    profit_history = []
    periods = []
    bet_periods = []
    bet_profits = []
    
    # 从第window_size+1期开始预测
    for i in range(window_size, len(df)):
        history_data = df.iloc[i-window_size:i]['numbers_list'].tolist()
        current_numbers = df.iloc[i]['numbers_list']
        current_period = df.iloc[i]['period']
        
        top_numbers, is_valid = get_top_numbers(history_data)
        
        periods.append(current_period)
        
        if not is_valid:
            # 跳过这期
            result = {
                'period': current_period,
                'action': 'skip',
                'profit_change': 0,
                'total_profit': total_profit,
                'consecutive_losses': consecutive_losses
            }
            results.append(result)
            profit_history.append(total_profit)
            continue
        
        # 进行预测
        prediction_numbers = top_numbers
        hit = check_prediction(prediction_numbers, current_numbers)
        
        if hit:
            profit_change = 19
            total_profit += profit_change
            consecutive_losses = 0
        else:
            profit_change = -2
            total_profit += profit_change
            consecutive_losses += 1
            max_consecutive_losses = max(max_consecutive_losses, consecutive_losses)
        
        result = {
            'period': current_period,
            'prediction': prediction_numbers,
            'hit': hit,
            'action': 'bet',
            'profit_change': profit_change,
            'total_profit': total_profit,
            'consecutive_losses': consecutive_losses
        }
        results.append(result)
        profit_history.append(total_profit)
        
        # 记录投注期次的数据
        bet_periods.append(current_period)
        bet_profits.append(total_profit)
    
    return results, profit_history, periods, bet_periods, bet_profits, max_consecutive_losses

def create_visualizations(results, profit_history, periods, bet_periods, bet_profits, max_consecutive_losses):
    """创建可视化图表"""
    
    # 创建图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('彩票回测分析结果', fontsize=16, fontweight='bold')
    
    # 1. 累计收益曲线
    ax1.plot(range(len(profit_history)), profit_history, 'b-', linewidth=2, label='累计收益')
    ax1.axhline(y=0, color='r', linestyle='--', alpha=0.7, label='盈亏平衡线')
    ax1.set_title('累计收益曲线')
    ax1.set_xlabel('期数')
    ax1.set_ylabel('累计收益')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    # 添加最终收益标注
    if profit_history:
        final_profit = profit_history[-1]
        ax1.annotate(f'最终收益: {final_profit:.1f}', 
                    xy=(len(profit_history)-1, final_profit),
                    xytext=(len(profit_history)*0.7, final_profit + 20),
                    arrowprops=dict(arrowstyle='->', color='red'),
                    fontsize=10, color='red', fontweight='bold')
    
    # 2. 投注期次收益分布
    bet_results = [r for r in results if r['action'] == 'bet']
    wins = [r for r in bet_results if r['hit']]
    losses = [r for r in bet_results if not r['hit']]
    
    categories = ['盈利', '亏损']
    values = [len(wins), len(losses)]
    colors = ['green', 'red']
    
    bars = ax2.bar(categories, values, color=colors, alpha=0.7)
    ax2.set_title('投注结果分布')
    ax2.set_ylabel('次数')
    
    # 添加数值标签
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{value}次', ha='center', va='bottom', fontweight='bold')
    
    # 3. 命中率统计
    total_bets = len(bet_results)
    hit_rate = len(wins) / total_bets * 100 if total_bets > 0 else 0
    
    labels = ['命中', '未命中']
    sizes = [len(wins), len(losses)]
    colors = ['lightgreen', 'lightcoral']
    explode = (0.05, 0)  # 突出显示命中部分
    
    wedges, texts, autotexts = ax3.pie(sizes, explode=explode, labels=labels, colors=colors,
                                      autopct='%1.1f%%', shadow=True, startangle=90)
    ax3.set_title(f'命中率分析 (总命中率: {hit_rate:.1f}%)')
    
    # 4. 连续亏损分析
    loss_streaks = []
    current_streak = 0
    
    for result in bet_results:
        if not result['hit']:
            current_streak += 1
        else:
            if current_streak > 0:
                loss_streaks.append(current_streak)
            current_streak = 0
    
    if current_streak > 0:
        loss_streaks.append(current_streak)
    
    if loss_streaks:
        streak_counts = Counter(loss_streaks)
        streaks = sorted(streak_counts.keys())
        counts = [streak_counts[s] for s in streaks]
        
        ax4.bar(streaks, counts, color='orange', alpha=0.7)
        ax4.set_title(f'连续亏损分布 (最大连续亏损: {max_consecutive_losses}期)')
        ax4.set_xlabel('连续亏损期数')
        ax4.set_ylabel('出现次数')
        ax4.set_xticks(streaks)
        
        # 添加数值标签
        for i, (streak, count) in enumerate(zip(streaks, counts)):
            ax4.text(streak, count + 0.1, str(count), ha='center', va='bottom')
    else:
        ax4.text(0.5, 0.5, '无连续亏损', ha='center', va='center', transform=ax4.transAxes)
        ax4.set_title('连续亏损分布')
    
    plt.tight_layout()
    # plt.savefig('select_2.png', dpi=300, bbox_inches='tight') # 先不保存
    plt.show()
    
    # 打印详细统计信息
    print("=" * 80)
    print("回测结果总结")
    print("=" * 80)
    print(f"总期数: {len(results)}")
    print(f"投注期数: {len(bet_results)}")
    print(f"跳过期数: {len(results) - len(bet_results)}")
    print(f"命中期数: {len(wins)}")
    print(f"命中率: {hit_rate:.2f}%")
    print(f"总收益: {profit_history[-1] if profit_history else 0:.1f}")
    print(f"连续亏损最大期数: {max_consecutive_losses}")
    
    if len(bet_results) > 0:
        avg_profit = profit_history[-1] / len(bet_results) if profit_history else 0
        print(f"平均每次投注收益: {avg_profit:.2f}")
    
    print(f"\n收益构成:")
    print(f"盈利总额: {len(wins) * 19:.1f} ({len(wins)}次 × 19)")
    print(f"亏损总额: {len(losses) * -2:.1f} ({len(losses)}次 × -2)")
    print(f"净收益: {profit_history[-1] if profit_history else 0:.1f}")

if __name__ == "__main__":
    results, profit_history, periods, bet_periods, bet_profits, max_consecutive_losses = backtest_with_visualization()
    create_visualizations(results, profit_history, periods, bet_periods, bet_profits, max_consecutive_losses)
