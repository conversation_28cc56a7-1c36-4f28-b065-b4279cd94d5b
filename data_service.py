#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快乐8数据服务模块
提供数据分析和处理功能
"""

import pandas as pd
import json
import os
from collections import Counter
from typing import List, Dict
import plotly.graph_objects as go
from plotly.utils import PlotlyJSONEncoder
import time

class Happy8DataService:
    def __init__(self, data_file: str = 'happy8_data.csv'):
        self.data_file = data_file
        self.df = None
        self._cache = {}

        # 快乐8中奖规则表 - 根据选择号码个数和匹配个数确定奖金
        self.prize_table = {
            1: {1: 4.6}, 
            2: {2: 19},  
            3: {3: 53, 2: 3},  
            4: {4: 100, 3: 5, 2: 3}, 
            5: {5: 1000, 4: 21, 3: 3},  
            6: {6: 3000, 5: 30, 4: 10, 3: 3},  
            7: {7: 10000, 6: 288, 5: 28, 4: 4, 0: 2}, 
            8: {8: 50000, 7: 800, 6: 88, 5: 10, 4: 3, 0: 2},  
            9: {9: 300000, 8: 2000, 7: 200, 6: 20, 5: 5, 4: 3, 0: 2},  
            10: {10: 5000000, 9: 8000, 8: 800, 7: 80, 6: 5, 5: 3, 0: 2}
        }

        # 从配置文件获取缓存时间
        try:
            from config import get_config
            config = get_config()
            self._cache_timeout = config.CACHE_TIMEOUT  # 10分钟缓存
            self._refresh_timeout = config.REFRESH_TIMEOUT  # 10分钟刷新限制
        except:
            self._cache_timeout = 600  # 默认10分钟缓存
            self._refresh_timeout = 600  # 默认10分钟刷新限制

        self._refresh_cache = {}  # 刷新操作缓存
        self.load_data()

    def _get_cache_key(self, method_name: str, *args, **kwargs) -> str:
        """生成缓存键"""
        return f"{method_name}_{hash(str(args) + str(sorted(kwargs.items())))}"

    def _get_cached_result(self, cache_key: str):
        """获取缓存结果"""
        if cache_key in self._cache:
            result, timestamp = self._cache[cache_key]
            if time.time() - timestamp < self._cache_timeout:
                return result
            else:
                del self._cache[cache_key]
        return None

    def _set_cache(self, cache_key: str, result):
        """设置缓存"""
        self._cache[cache_key] = (result, time.time())

    def clear_cache(self):
        """清除缓存"""
        self._cache.clear()

    def _can_refresh(self) -> bool:
        """检查是否可以刷新数据（10分钟限制）"""
        last_refresh = self._refresh_cache.get('last_refresh_time', 0)
        return time.time() - last_refresh >= self._refresh_timeout

    def _set_refresh_time(self):
        """设置最后刷新时间"""
        self._refresh_cache['last_refresh_time'] = time.time()

    def get_refresh_remaining_time(self) -> int:
        """获取距离下次可刷新的剩余时间（秒）"""
        last_refresh = self._refresh_cache.get('last_refresh_time', 0)
        elapsed = time.time() - last_refresh
        remaining = max(0, self._refresh_timeout - elapsed)
        return int(remaining)

    def calculate_prize(self, selected_count: int, match_count: int) -> float:
        """
        根据选择号码个数和匹配个数计算奖金

        Args:
            selected_count: 选择的号码个数
            match_count: 匹配的号码个数

        Returns:
            奖金金额（元）
        """
        if selected_count not in self.prize_table:
            return 0.0

        if match_count not in self.prize_table[selected_count]:
            return 0.0

        return self.prize_table[selected_count][match_count]

    def calculate_total_winnings(self, selected_numbers: List[int], distribution: Dict[int, int]) -> Dict[str, float]:
        """
        计算总中奖金额和消费金额

        Args:
            selected_numbers: 选择的号码列表
            distribution: 匹配数量分布

        Returns:
            包含消费金额和中奖金额的字典
        """
        selected_count = len(selected_numbers)
        total_periods = sum(distribution.values())

        # 计算消费金额：期数 × 2元
        cost_amount = total_periods * 2

        # 计算中奖金额
        total_winnings = 0.0
        for match_count, count in distribution.items():
            prize_per_period = self.calculate_prize(selected_count, match_count)
            total_winnings += prize_per_period * count

        return {
            'cost_amount': cost_amount,
            'total_winnings': total_winnings,
            'net_profit': total_winnings - cost_amount
        }

    def load_data(self) -> bool:
        """加载开奖数据"""
        try:
            if os.path.exists(self.data_file):
                self.df = pd.read_csv(self.data_file)

                # 修复时间顺序：按期号降序排列，最新的在前面
                self.df = self.df.sort_values('period', ascending=False).reset_index(drop=True)

                print(f"成功加载 {len(self.df)} 期开奖数据")
                return True
            else:
                print(f"数据文件 {self.data_file} 不存在")
                return False
        except Exception as e:
            print(f"加载数据失败: {e}")
            return False
    
    def get_basic_stats(self) -> Dict:
        """获取基本统计信息"""
        if self.df is None:
            return {}

        return {
            'total_periods': int(len(self.df)),
            'latest_period': str(self.df.iloc[0]['period']) if len(self.df) > 0 else None,
            'earliest_period': str(self.df.iloc[-1]['period']) if len(self.df) > 0 else None
        }
    
    def get_number_frequency(self, limit: int = 80) -> List[Dict]:
        """获取号码出现频率"""
        if self.df is None:
            return []

        all_numbers = []
        for _, row in self.df.iterrows():
            numbers = eval(row['numbers'])

            # 确保每期只有20个号码，如果超过20个就从后往前取20个
            if len(numbers) > 20:
                numbers = numbers[-20:]

            all_numbers.extend([int(num) for num in numbers])

        frequency = Counter(all_numbers)
        total_periods = len(self.df)

        result = []
        for num, count in frequency.most_common(limit):
            result.append({
                'number': num,
                'count': count,
                'frequency': round((count / total_periods) * 100, 2)
            })

        return result
    
    def get_recent_trends(self, periods: int = 50) -> List[Dict]:
        """获取最近趋势（现在数据已按期号降序排列，head()获取的就是最新数据）"""
        if self.df is None:
            return []

        # 获取最近的periods期数据（现在已经是按期号降序排列）
        recent_df = self.df.head(periods)
        recent_numbers = []

        for _, row in recent_df.iterrows():
            numbers = eval(row['numbers'])

            # 确保每期只有20个号码，如果超过20个就从后往前取20个
            if len(numbers) > 20:
                numbers = numbers[-20:]

            recent_numbers.extend([int(num) for num in numbers])

        frequency = Counter(recent_numbers)
        total_periods = len(recent_df)

        result = []
        for num, count in frequency.most_common(20):
            result.append({
                'number': num,
                'count': count,
                'frequency': round((count / total_periods) * 100, 2)
            })

        return result
    
    def get_latest_results(self, limit: int = 10) -> List[Dict]:
        """获取最新开奖结果"""
        if self.df is None:
            return []

        results = []
        for i in range(min(limit, len(self.df))):
            row = self.df.iloc[i]
            numbers = eval(row['numbers'])

            # 确保每期只有20个号码，如果超过20个就从后往前取20个
            if len(numbers) > 20:
                numbers = numbers[-20:]
                print(f"期号 {row['period']} 号码超过20个，已截取最后20个")

            results.append({
                'period': str(row['period']),
                'numbers': [int(num) for num in numbers],
                'numbers_str': ' '.join([f"{int(num):02d}" for num in numbers]),
                'count': len(numbers)
            })

        return results
    

    
    def create_frequency_chart(self, top_n: int = 20) -> str:
        """创建号码频率图表（带缓存）"""
        cache_key = self._get_cache_key('frequency_chart', top_n)
        cached_result = self._get_cached_result(cache_key)
        if cached_result is not None:
            return cached_result

        frequency_data = self.get_number_frequency(top_n)

        if not frequency_data:
            return "{}"

        numbers = [f"{item['number']:02d}" for item in frequency_data]
        frequencies = [item['frequency'] for item in frequency_data]

        fig = go.Figure(data=[
            go.Bar(
                x=numbers,
                y=frequencies,
                text=[f"{freq}%" for freq in frequencies],
                textposition='auto',
                marker_color='rgba(55, 128, 191, 0.7)',
                marker_line_color='rgba(55, 128, 191, 1.0)',
                marker_line_width=1.5
            )
        ])

        fig.update_layout(
            title=f'快乐8号码出现频率 TOP {top_n}',
            xaxis_title='号码',
            yaxis_title='出现频率 (%)',
            template='plotly_white',
            height=400
        )

        result = json.dumps(fig, cls=PlotlyJSONEncoder)
        self._set_cache(cache_key, result)
        return result
    
    def create_trend_chart(self, periods: int = 50) -> str:
        """创建最近趋势图表"""
        trend_data = self.get_recent_trends(periods)

        if not trend_data:
            return "{}"

        numbers = [f"{item['number']:02d}" for item in trend_data[:10]]
        frequencies = [item['frequency'] for item in trend_data[:10]]

        fig = go.Figure(data=[
            go.Bar(
                x=numbers,
                y=frequencies,
                text=[f"{freq}%" for freq in frequencies],
                textposition='auto',
                marker_color='rgba(255, 127, 14, 0.7)',
                marker_line_color='rgba(255, 127, 14, 1.0)',
                marker_line_width=1.5
            )
        ])

        fig.update_layout(
            title=f'最近{periods}期热门号码 TOP 10',
            xaxis_title='号码',
            yaxis_title='出现频率 (%)',
            template='plotly_white',
            height=400
        )

        return json.dumps(fig, cls=PlotlyJSONEncoder)

    def create_multiple_trend_charts(self) -> Dict[str, str]:
        """创建多个时间段的趋势图表"""
        periods_list = [3, 5, 10, 15, 20, 30]
        charts = {}

        for periods in periods_list:
            charts[f'trend_{periods}'] = self.create_trend_chart(periods)

        return charts

    def analyze_selected_numbers(self, selected_numbers: List[int], periods: int) -> Dict:
        """分析选中号码在指定期数中的出现情况"""
        if self.df is None:
            return {}

        # 获取最近的periods期数据
        recent_df = self.df.head(periods)

        # 分析结果
        details = []
        # 动态创建分布统计，支持0到选中号码数量的所有可能匹配
        distribution = {i: 0 for i in range(len(selected_numbers) + 1)}
        trend = []

        total_matches = 0
        full_matches = 0
        partial_matches = 0

        for _, row in recent_df.iterrows():
            numbers = eval(row['numbers'])

            # 确保每期只有20个号码
            if len(numbers) > 20:
                numbers = numbers[-20:]

            # 转换为整数列表
            period_numbers = [int(num) for num in numbers]

            # 计算匹配情况
            matched_numbers = [num for num in selected_numbers if num in period_numbers]
            match_count = len(matched_numbers)
            match_rate = round((match_count / len(selected_numbers)) * 100, 1)

            # 统计
            if match_count > 0:
                total_matches += 1
                if match_count == len(selected_numbers):
                    full_matches += 1
                else:
                    partial_matches += 1

            # 分布统计
            distribution[match_count] += 1

            # 详细结果
            details.append({
                'period': str(row['period']),
                'numbers': period_numbers,
                'match_count': match_count,
                'matched_numbers': matched_numbers,
                'match_rate': match_rate
            })

            # 趋势数据
            trend.append({
                'period': str(row['period']),
                'match_count': match_count
            })

        # 计算总体匹配率
        overall_match_rate = round((total_matches / len(recent_df)) * 100, 1) if len(recent_df) > 0 else 0

        # 计算中奖金额和消费金额
        financial_info = self.calculate_total_winnings(selected_numbers, distribution)

        # 构建返回结果
        result = {
            'summary': {
                'total_matches': total_matches,
                'full_matches': full_matches,
                'partial_matches': partial_matches,
                'match_rate': overall_match_rate,
                'cost_amount': financial_info['cost_amount'],
                'total_winnings': financial_info['total_winnings'],
                'net_profit': financial_info['net_profit']
            },
            'details': details,
            'distribution': distribution,
            'trend': trend
        }

        return result

    def refresh_data(self) -> Dict[str, any]:
        """刷新数据（重新运行爬虫）带缓存限制"""
        # 检查是否可以刷新
        if not self._can_refresh():
            remaining_time = self.get_refresh_remaining_time()
            return {
                'success': False,
                'message': f'数据刷新过于频繁，请等待 {remaining_time} 秒后再试',
                'remaining_time': remaining_time
            }

        try:
            print("开始刷新数据...")
            from happy8_crawler import Happy8Crawler
            crawler = Happy8Crawler()
            crawler.run(500)

            # 设置刷新时间
            self._set_refresh_time()

            # 清除缓存
            self.clear_cache()

            # 重新加载数据
            success = self.load_data()

            if success:
                return {
                    'success': True,
                    'message': '数据刷新成功',
                    'remaining_time': 0
                }
            else:
                return {
                    'success': False,
                    'message': '数据加载失败',
                    'remaining_time': 0
                }

        except Exception as e:
            print(f"刷新数据失败: {e}")
            return {
                'success': False,
                'message': f'刷新数据失败: {str(e)}',
                'remaining_time': 0
            }
