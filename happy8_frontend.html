<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快乐8数据分析系统</title>
    <link href="/static/bootstrap.min.css" rel="stylesheet">
    <link href="/static/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 20px auto;
            max-width: 1400px;
            padding: 30px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .header h1 {
            color: #2c3e50;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #6c757d;
            font-size: 1.1rem;
        }
        
        .control-panel {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            border: 2px solid #e9ecef;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
        }

        .form-label {
            color: #495057;
            font-size: 0.9rem;
            margin-bottom: 8px;
        }
        
        .btn-custom {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
            color: white;
        }
        
        .btn-custom:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: 0 2px 10px rgba(102, 126, 234, 0.2);
        }
        
        .table-container {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid #e9ecef;
        }
        
        .table-responsive {
            max-height: 70vh;
            overflow-y: auto;
        }
        
        .table {
            margin-bottom: 0;
            font-size: 0.9rem;
        }
        
        .table thead th {
            background: #2c3e50;
            color: white;
            border: none;
            padding: 15px 10px;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
            text-align: center;
            vertical-align: middle;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .table thead th:hover {
            background: #34495e;
        }

        .table thead th.sortable::after {
            content: ' ↕';
            opacity: 0.5;
            font-size: 0.8em;
        }

        .table thead th.sort-asc::after {
            content: ' ↑';
            opacity: 1;
            color: #3498db;
        }

        .table thead th.sort-desc::after {
            content: ' ↓';
            opacity: 1;
            color: #3498db;
        }
        
        .table tbody td {
            padding: 12px 10px;
            vertical-align: middle;
            text-align: center;
            border-bottom: 1px solid #f1f3f4;
        }
        
        .table tbody tr:hover {
            background-color: #f8f9fa;
        }
        
        .profit-positive {
            color: #28a745;
            font-weight: 600;
        }
        
        .profit-negative {
            color: #dc3545;
            font-weight: 600;
        }
        
        .row-low-hit-rate {
            color: #6c757d !important;
            opacity: 0.7;
        }

        .row-low-hit-rate td {
            color: #6c757d !important;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }
        
        .loading i {
            font-size: 3rem;
            margin-bottom: 20px;
            animation: spin 2s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .alert-custom {
            border-radius: 15px;
            border: none;
            padding: 15px 20px;
            margin-bottom: 20px;
        }
        
        .filter-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .form-select, .form-control {
            border-radius: 25px;
            border: 2px solid #e9ecef;
            padding: 12px 20px;
            background: white;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .form-select:focus, .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            transform: translateY(-1px);
        }

        .form-select:hover, .form-control:hover {
            border-color: #667eea;
            transform: translateY(-1px);
        }

        .btn-outline-secondary {
            background: white;
            border: 2px solid #e9ecef;
            color: #6c757d;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-outline-secondary:hover {
            background: #f8f9fa;
            border-color: #667eea;
            color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
        }


        

        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                padding: 20px;
                border-radius: 15px;
            }

            .table {
                font-size: 0.8rem;
            }

            .table thead th,
            .table tbody td {
                padding: 8px 3px;
            }

            .btn-custom {
                padding: 10px 20px;
                font-size: 0.9rem;
                width: 100%;
                margin-bottom: 10px;
            }

            .control-panel {
                padding: 15px;
            }

            .control-panel .row > div {
                margin-bottom: 15px;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }

            .stats-card {
                padding: 15px;
            }

            .stats-card .amount {
                font-size: 1.4rem;
            }

            .bet-amount-input {
                width: 3rem;
            }

            .bet-status-select {
                width: 70px;
                font-size: 0.7rem;
            }
        }
        
        @media (max-width: 576px) {
            .table {
                font-size: 0.7rem;
            }
            
            .table thead th,
            .table tbody td {
                padding: 6px 3px;
            }
            
            .main-container {
                padding: 15px;
            }
        }
        
        /* 投注相关样式 */
        .bet-amount-input {
            padding: 4px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-align: center;
        }

        .bet-status-select {
            width: 90px;
            padding: 4px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.8rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border: 1px solid #e9ecef;
            text-align: center;
        }

        .stats-card .icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }

        .stats-card .amount {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stats-card .label {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .stats-card.investment .icon { color: #3498db; }
        .stats-card.investment .amount { color: #3498db; }

        .stats-card.win .icon { color: #27ae60; }
        .stats-card.win .amount { color: #27ae60; }

        .stats-card.pending .icon { color: #f39c12; }
        .stats-card.pending .amount { color: #f39c12; }

        .stats-card.profit .icon { color: #e74c3c; }
        .stats-card.profit .amount { color: #e74c3c; }

        .stats-card.profit.positive .icon { color: #27ae60; }
        .stats-card.profit.positive .amount { color: #27ae60; }

        /* 表格列宽优化 */
        .col-window-size { width: 12%; }
        .col-bet-count { width: 12%; }
        .col-hit-rate { width: 12%; }
        .col-max-loss { width: 12%; }
        .col-current-fail { width: 12%; }
        .col-prediction { width: 20%; }
        .col-bet-amount { width: 10%; }
        .col-bet-status { width: 10%; }

        /* 投注状态图标样式 */
        .bet-status-icon {
            font-size: 1.2rem;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
            display: inline-block;
            width: 30px;
            height: 30px;
            line-height: 20px;
            text-align: center;
        }

        .bet-status-icon:hover {
            transform: scale(1.1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .bet-status-icon.disabled {
            cursor: not-allowed;
            opacity: 0.3;
        }

        .bet-status-icon.disabled:hover {
            transform: none;
            box-shadow: none;
        }

        /* 不同状态的图标颜色 */
        .status-none {
            color: #6c757d;
            background: #f8f9fa;
        }

        .status-pending {
            color: #ffc107;
            background: #fff3cd;
        }

        .status-win {
            color: #28a745;
            background: #d4edda;
        }

        .status-lose {
            color: #dc3545;
            background: #f8d7da;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- 头部 -->
            <div class="header">
                <h1><i class="fas fa-chart-line me-3"></i>快乐8数据分析系统</h1>
                <p>基于窗口大小分析的快乐8数据预测与回测系统</p>
            </div>

            <!-- 投注统计卡片 -->
            <div id="statsGrid" class="stats-grid">
                <div class="stats-card investment">
                    <div class="icon"><i class="fas fa-coins"></i></div>
                    <div id="totalInvestment" class="amount">¥0</div>
                    <div class="label">总投入金额</div>
                </div>
                <div class="stats-card win">
                    <div class="icon"><i class="fas fa-trophy"></i></div>
                    <div id="winAmount" class="amount">¥0</div>
                    <div class="label">中奖金额</div>
                </div>
                <div class="stats-card pending">
                    <div class="icon"><i class="fas fa-clock"></i></div>
                    <div id="pendingAmount" class="amount">¥0</div>
                    <div class="label">未开奖金额</div>
                </div>
                <div id="profitCard" class="stats-card profit">
                    <div class="icon"><i class="fas fa-chart-line"></i></div>
                    <div id="profitLoss" class="amount">¥0</div>
                    <div class="label">已确定盈亏</div>
                    <div class="small text-muted mt-1" style="font-size: 0.7rem;">
                        <i class="fas fa-info-circle me-1"></i>不含未开奖金额
                    </div>
                </div>
            </div>
       
            <!-- 控制面板 -->
            <div class="control-panel">
                <div class="row align-items-end g-3">
                    <div class="col-md-4 col-12">
                        <button id="manualRefreshBtn" class="btn btn-custom w-100">
                            <i class="fas fa-sync-alt me-2"></i>刷新数据
                        </button>
                    </div>
                    <div class="col-md-4 col-12">
                        <button id="exportBtn" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-download me-2"></i>导出数据
                        </button>
                    </div>
                    <div class="col-md-4 col-12">
                        <label for="periodInput" class="form-label mb-2 fw-semibold text-muted">
                            <i class="fas fa-calendar-alt me-1"></i>选择期号
                        </label>
                        <div class="input-group">
                            <input type="text"
                                   id="periodInput"
                                   class="form-control"
                                   placeholder="输入期号"
                                   list="periodList">
                            <button class="btn btn-outline-secondary" type="button" id="latestPeriodBtn">
                                <i class="fas fa-clock"></i> 最新
                            </button>
                        </div>
                        <datalist id="periodList">
                            <!-- 期号选项将动态加载 -->
                        </datalist>
                    </div>
                </div>
            </div>
            
            <!-- 消息提示 -->
            <div id="messageArea"></div>
            
            <!-- 数据表格 -->
            <div class="table-container">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th class="col-window-size sortable" data-sort="window_size">窗口大小</th>
                                <th class="col-bet-count sortable" data-sort="bet_count">投注次数</th>
                                <th class="col-hit-rate sortable" data-sort="hit_rate">命中率</th>
                                <th class="col-max-loss sortable" data-sort="max_consecutive_losses">最大连败</th>
                                <th class="col-current-fail sortable" data-sort="current_consecutive_failures">当前连败</th>
                                <th class="col-prediction">下期预测</th>
                                <th class="col-bet-amount">投注金额</th>
                                <th class="col-bet-status">投注状态</th>
                            </tr>
                        </thead>
                        <tbody id="dataTableBody">
                            <tr>
                                <td colspan="8" class="loading">
                                    <i class="fas fa-database"></i>
                                    <div>暂无数据，请点击"刷新数据"按钮</div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/bootstrap.bundle.min.js"></script>
    <script>
        let currentData = [];
        let allPeriods = [];
        let currentSort = { field: 'hit_rate', direction: 'desc' };
        let updateTimeout = null; // 防抖定时器

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadPeriods();
            loadLatestData();
            loadBetStats();

            // 绑定事件
            document.getElementById('manualRefreshBtn').addEventListener('click', manualRefresh);
            document.getElementById('exportBtn').addEventListener('click', exportData);
            document.getElementById('periodInput').addEventListener('change', onPeriodChange);
            document.getElementById('latestPeriodBtn').addEventListener('click', loadLatestPeriod);

            // 绑定表头点击排序事件
            document.querySelectorAll('.sortable').forEach(th => {
                th.addEventListener('click', function() {
                    const sortField = this.getAttribute('data-sort');
                    handleTableSort(sortField);
                });
            });
        });
        
        // 显示消息
        function showMessage(message, type = 'info') {
            const messageArea = document.getElementById('messageArea');
            const alertClass = type === 'error' ? 'alert-danger' : 
                              type === 'success' ? 'alert-success' : 'alert-info';
            
            messageArea.innerHTML = `
                <div class="alert ${alertClass} alert-custom alert-dismissible fade show" role="alert">
                    <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : 
                                      type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            // 自动隐藏消息
            setTimeout(() => {
                const alert = messageArea.querySelector('.alert');
                if (alert) {
                    alert.remove();
                }
            }, 5000);
        }
        
        // 加载期号列表（只加载最近10个期号以提高性能）
        async function loadPeriods() {
            try {
                const response = await fetch('/api/periods?limit=10');
                const result = await response.json();

                if (result.success) {
                    allPeriods = result.periods;
                    updatePeriodDatalist();
                }
            } catch (error) {
                console.error('加载期号列表失败:', error);
            }
        }

        // 更新期号数据列表
        function updatePeriodDatalist() {
            const datalist = document.getElementById('periodList');
            datalist.innerHTML = '';

            allPeriods.forEach(period => {
                const option = document.createElement('option');
                option.value = period;
                datalist.appendChild(option);
            });
        }

        // 加载最新期号
        function loadLatestPeriod() {
            document.getElementById('periodInput').value = '';
            loadLatestData();
        }
        
        // 加载最新数据
        async function loadLatestData() {
            try {
                const response = await fetch('/api/results');
                const result = await response.json();

                if (result.success && result.data) {
                    currentData = result.data;
                    updateTable();
                    showMessage(result.message, 'success');
                } else {
                    showMessage(result.message, 'error');
                }
            } catch (error) {
                console.error('加载数据失败:', error);
                showMessage('加载数据失败，请检查网络连接', 'error');
            }
        }

        // 手动刷新（定时器刷新）
        async function manualRefresh() {
            const btn = document.getElementById('manualRefreshBtn');
            const originalText = btn.innerHTML;

            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>刷新中...';
            btn.disabled = true;

            try {
                showMessage('正在启动后台数据刷新，请稍后查看结果...', 'info');

                const response = await fetch('/api/manual-refresh', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showMessage(result.message + ' 正在等待数据更新完成...', 'info');

                    // 等待30秒后自动刷新页面数据
                    setTimeout(async () => {
                        try {
                            await loadPeriods();
                            await loadLatestData();
                            await loadBetStats();
                            showMessage('数据已更新完成！', 'success');
                        } catch (error) {
                            showMessage('自动刷新页面数据失败，请手动刷新页面', 'error');
                        }
                    }, 30000); // 30秒后自动刷新
                } else {
                    showMessage(result.message, 'error');
                }
            } catch (error) {
                console.error('手动刷新失败:', error);
                showMessage('手动刷新失败，请检查网络连接', 'error');
            } finally {
                btn.innerHTML = originalText;
                btn.disabled = false;
            }
        }



        // 期号变化事件
        async function onPeriodChange() {
            const selectedPeriod = document.getElementById('periodInput').value.trim();

            if (selectedPeriod === '') {
                // 加载最新数据
                await loadLatestData();
            } else {
                // 验证期号格式（7位数字）
                if (!/^\d{7}$/.test(selectedPeriod)) {
                    showMessage('期号格式错误，请输入7位数字', 'error');
                    return;
                }

                // 加载指定期号数据
                try {
                    const response = await fetch(`/api/results/${selectedPeriod}`);
                    const result = await response.json();

                    if (result.success && result.data) {
                        currentData = result.data;
                        updateTable();
                        showMessage(result.message, 'success');
                    } else {
                        showMessage(result.message, 'error');
                    }
                } catch (error) {
                    console.error('加载期号数据失败:', error);
                    showMessage('加载期号数据失败', 'error');
                }
            }
        }



        // 处理表头点击排序
        function handleTableSort(field) {
            if (currentSort.field === field) {
                currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
            } else {
                currentSort.field = field;
                currentSort.direction = 'desc';
            }

            updateTable();
            updateTableHeaders();
        }

        // 更新表头排序指示器
        function updateTableHeaders() {
            document.querySelectorAll('.sortable').forEach(th => {
                th.classList.remove('sort-asc', 'sort-desc');
                if (th.getAttribute('data-sort') === currentSort.field) {
                    th.classList.add(currentSort.direction === 'asc' ? 'sort-asc' : 'sort-desc');
                }
            });
        }

        // 更新表格
        function updateTable() {
            const tbody = document.getElementById('dataTableBody');

            if (!currentData || currentData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" class="loading">
                            <i class="fas fa-database"></i>
                            <div>暂无数据</div>
                        </td>
                    </tr>
                `;
                return;
            }

            // 排序数据
            const sortedData = [...currentData].sort((a, b) => {
                const field = currentSort.field;
                const direction = currentSort.direction;

                let aVal = a[field];
                let bVal = b[field];

                // 数值比较
                if (typeof aVal === 'number' && typeof bVal === 'number') {
                    return direction === 'asc' ? aVal - bVal : bVal - aVal;
                }

                // 字符串比较
                return direction === 'asc' ?
                    String(aVal).localeCompare(String(bVal)) :
                    String(bVal).localeCompare(String(aVal));
            });

            // 生成表格行
            tbody.innerHTML = sortedData.map(row => {
                const isLowHitRate = row.hit_rate < 50;
                const rowClass = isLowHitRate ? 'row-low-hit-rate' : '';

                return `
                    <tr class="${rowClass}">
                        <td><strong>${row.window_size}</strong></td>
                        <td>${row.bet_count}</td>
                        <td>${row.hit_rate.toFixed(2)}%</td>
                        <td>${row.max_consecutive_losses}</td>
                        <td>${row.current_consecutive_failures}</td>
                        <td><code>${row.next_prediction || '-'}</code></td>
                        <td>
                            <input type="number"
                                   class="bet-amount-input"
                                   value="${row.bet_amount || 0}"
                                   min="0"
                                   step="1"
                                   oninput="debouncedUpdateBetInfo('${row.latest_period}', ${row.window_size}, this.value, '${row.bet_status || '未开奖'}')">
                        </td>
                        <td>
                            ${generateBetStatusIcon(row)}
                        </td>
                    </tr>
                `;
            }).join('');

            updateTableHeaders();
        }

        // 加载投注统计
        async function loadBetStats() {
            try {
                const response = await fetch('/api/bet-stats');
                const result = await response.json();

                if (result.success) {
                    updateStatsDisplay(result.data);
                }
            } catch (error) {
                console.error('加载投注统计失败:', error);
            }
        }

        // 更新统计显示
        function updateStatsDisplay(stats) {
            document.getElementById('totalInvestment').textContent = `¥${stats.total_investment}`;
            document.getElementById('winAmount').textContent = `¥${stats.win_amount}`;
            document.getElementById('pendingAmount').textContent = `¥${stats.pending_amount}`;
            document.getElementById('profitLoss').textContent = `¥${stats.profit_loss}`;

            // 更新盈亏卡片样式
            const profitCard = document.getElementById('profitCard');
            if (stats.profit_loss >= 0) {
                profitCard.classList.add('positive');
            } else {
                profitCard.classList.remove('positive');
            }
        }

        // 生成投注状态图标
        function generateBetStatusIcon(row) {
            const betAmount = row.bet_amount || 0;
            const betStatus = row.bet_status || '未开奖';
            const hasAmount = betAmount > 0;

            // 如果没有投注金额，显示禁用的图标
            if (!hasAmount) {
                return `<i class="bet-status-icon disabled fas fa-minus-circle status-none"
                           title="无投注金额"></i>`;
            }

            // 根据状态显示不同图标
            let iconClass, statusClass, title;
            switch (betStatus) {
                case '中':
                    iconClass = 'fas fa-check-circle';
                    statusClass = 'status-win';
                    title = '中奖';
                    break;
                case '未中':
                    iconClass = 'fas fa-times-circle';
                    statusClass = 'status-lose';
                    title = '未中奖';
                    break;
                default: // '未开奖'
                    iconClass = 'fas fa-clock';
                    statusClass = 'status-pending';
                    title = '未开奖';
                    break;
            }

            return `<i class="bet-status-icon ${iconClass} ${statusClass}"
                       title="${title}"
                       onclick="toggleBetStatus('${row.latest_period}', ${row.window_size}, '${betStatus}', ${betAmount})"></i>`;
        }

        // 切换投注状态
        function toggleBetStatus(latestPeriod, windowSize, currentStatus, betAmount) {
            // 如果没有投注金额，不允许切换
            if (!betAmount || betAmount <= 0) {
                showMessage('请先设置投注金额', 'error');
                return;
            }

            // 状态循环：未开奖 -> 中 -> 未中 -> 未开奖
            let nextStatus;
            switch (currentStatus) {
                case '未开奖':
                    nextStatus = '中';
                    break;
                case '中':
                    nextStatus = '未中';
                    break;
                case '未中':
                    nextStatus = '未开奖';
                    break;
                default:
                    nextStatus = '未开奖';
                    break;
            }

            updateBetInfo(latestPeriod, windowSize, betAmount, nextStatus);
        }

        // 防抖更新投注信息
        function debouncedUpdateBetInfo(latestPeriod, windowSize, betAmount, betStatus) {
            if (updateTimeout) {
                clearTimeout(updateTimeout);
            }
            updateTimeout = setTimeout(() => {
                updateBetInfo(latestPeriod, windowSize, betAmount, betStatus);
            }, 500); // 500ms 防抖
        }

        // 更新投注信息
        async function updateBetInfo(latestPeriod, windowSize, betAmount, betStatus) {
            try {
                const response = await fetch('/api/bet-info', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        latest_period: latestPeriod,
                        window_size: windowSize,
                        bet_amount: parseInt(betAmount) || 0,
                        bet_status: betStatus
                    })
                });

                const result = await response.json();

                if (result.success) {
                    // 更新当前数据中的对应记录
                    const dataIndex = currentData.findIndex(item =>
                        item.latest_period === latestPeriod && item.window_size === windowSize);
                    if (dataIndex !== -1) {
                        currentData[dataIndex].bet_amount = parseInt(betAmount) || 0;
                        currentData[dataIndex].bet_status = betStatus;
                        updateTable(); // 重新渲染表格
                    }

                    // 重新加载统计数据
                    loadBetStats();
                } else {
                    showMessage(result.message, 'error');
                }
            } catch (error) {
                console.error('更新投注信息失败:', error);
                showMessage('更新投注信息失败', 'error');
            }
        }



        // 导出数据
        function exportData() {
            if (!currentData || currentData.length === 0) {
                showMessage('没有数据可导出', 'error');
                return;
            }

            // 准备CSV数据
            const headers = [
                '窗口大小', '投注次数', '命中率', '最大连败', '当前连败', '下期预测', '投注金额', '投注状态'
            ];

            const csvContent = [
                headers.join(','),
                ...currentData.map(row => [
                    row.window_size,
                    row.bet_count,
                    row.hit_rate.toFixed(2),
                    row.max_consecutive_losses,
                    row.current_consecutive_failures,
                    `"${row.next_prediction || ''}"`,
                    row.bet_amount || 0,
                    row.bet_status || '未开奖'
                ].join(','))
            ].join('\n');

            // 创建下载链接
            const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);

            link.setAttribute('href', url);
            link.setAttribute('download', `快乐8分析结果_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showMessage('数据导出成功', 'success');
        }
    </script>
</body>
</html>
