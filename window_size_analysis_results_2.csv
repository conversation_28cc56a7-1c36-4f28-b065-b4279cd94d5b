analysis_date,latest_period,window_size,final_profit,bet_count,win_count,loss_count,hit_rate,max_consecutive_losses,current_consecutive_failures,loss_streak_counts,next_prediction
2025-06-07,2025146,2,468,39,26,13,66.67,4,0,"{1: 4, 3: 1, 2: 1, 4: 1}",
2025-06-07,2025146,3,583,55,33,22,60.0,3,1,"{1: 8, 3: 2, 2: 4}",
2025-06-07,2025146,4,744,69,42,27,60.87,4,0,"{2: 3, 3: 3, 1: 8, 4: 1}",
2025-06-07,2025146,5,671,53,37,16,69.81,5,0,"{1: 7, 5: 1, 2: 2}",
2025-06-07,2025146,6,535,79,33,46,41.77,8,2,"{1: 8, 4: 2, 3: 4, 2: 5, 8: 1}",
2025-06-07,2025146,7,560,56,32,24,57.14,5,0,"{1: 9, 5: 1, 2: 2, 3: 2}",
2025-06-07,2025146,8,822,72,46,26,63.89,5,0,"{1: 9, 2: 6, 5: 1}",
2025-06-07,2025146,9,516,57,30,27,52.63,5,0,"{1: 9, 3: 1, 4: 2, 2: 1, 5: 1}",
2025-06-07,2025146,10,514,58,30,28,51.72,3,0,"{3: 5, 2: 3, 1: 7}",
2025-06-07,2025146,11,618,69,36,33,52.17,4,0,"{4: 2, 1: 8, 2: 4, 3: 3}",
2025-06-07,2025146,12,615,60,35,25,58.33,4,3,"{2: 4, 1: 7, 4: 1, 3: 2}",
2025-06-07,2025146,13,680,59,38,21,64.41,5,0,"{2: 3, 1: 6, 4: 1, 5: 1}",
2025-06-07,2025146,14,361,40,21,19,52.5,4,1,"{3: 1, 1: 6, 2: 3, 4: 1}",
2025-06-07,2025146,15,630,63,36,27,57.14,5,0,"{1: 12, 3: 2, 2: 2, 5: 1}",
2025-06-07,2025146,16,457,55,27,28,49.09,3,0,"{1: 5, 2: 7, 3: 3}",
2025-06-07,2025146,17,465,51,27,24,52.94,5,1,"{1: 9, 5: 1, 2: 3, 4: 1}",
2025-06-07,2025146,18,568,52,32,20,61.54,5,2,"{1: 11, 5: 1, 2: 2}",
2025-06-07,2025146,19,483,42,27,15,64.29,3,1,"{1: 8, 2: 2, 3: 1}",
2025-06-07,2025146,20,584,44,32,12,72.73,2,0,"{1: 6, 2: 3}",
2025-06-07,2025146,21,494,47,28,19,59.57,4,0,"{1: 4, 2: 4, 3: 1, 4: 1}",
2025-06-07,2025146,22,270,33,16,17,48.48,4,0,"{4: 2, 1: 4, 2: 1, 3: 1}",
2025-06-07,2025146,23,437,44,25,19,56.82,5,0,"{1: 5, 4: 1, 2: 1, 5: 1, 3: 1}",
2025-06-07,2025146,24,403,40,23,17,57.5,3,0,"{1: 8, 3: 3}",
2025-06-07,2025146,25,314,32,18,14,56.25,4,0,"{1: 8, 4: 1, 2: 1}",
2025-06-07,2025146,26,399,42,23,19,54.76,4,1,"{1: 6, 2: 3, 3: 1, 4: 1}",
2025-06-07,2025146,27,458,44,26,18,59.09,4,0,"{3: 2, 2: 1, 1: 6, 4: 1}",
2025-06-07,2025146,28,308,35,18,17,51.43,4,4,"{3: 2, 2: 3, 1: 1, 4: 1}",
2025-06-07,2025146,29,323,38,19,19,50.0,4,0,"{1: 4, 3: 1, 2: 4, 4: 1}",
2025-06-07,2025146,30,395,44,23,21,52.27,4,0,"{3: 2, 1: 5, 2: 3, 4: 1}",
2025-06-07,2025146,31,395,44,23,21,52.27,3,1,"{1: 9, 2: 3, 3: 2}",
2025-06-07,2025146,32,426,39,24,15,61.54,3,0,"{1: 4, 2: 1, 3: 3}",
2025-06-07,2025146,33,520,55,30,25,54.55,3,0,"{1: 7, 2: 6, 3: 2}",
2025-06-07,2025146,34,481,43,27,16,62.79,2,0,"{1: 6, 2: 5}",
2025-06-07,2025146,35,401,41,23,18,56.1,3,3,"{1: 10, 2: 1, 3: 2}","[21, 34, 52]"
2025-06-07,2025146,36,345,27,19,8,70.37,2,2,"{1: 6, 2: 1}",
2025-06-07,2025146,37,244,25,14,11,56.0,4,0,"{4: 1, 1: 2, 3: 1, 2: 1}",
2025-06-07,2025146,38,352,34,20,14,58.82,2,2,"{2: 5, 1: 4}","[21, 34, 52]"
2025-06-07,2025146,39,280,28,16,12,57.14,4,0,"{1: 2, 3: 2, 4: 1}",
2025-06-07,2025146,40,302,38,18,20,47.37,6,2,"{1: 3, 2: 4, 6: 1, 3: 1}",
2025-06-07,2025146,41,337,31,19,12,61.29,2,0,"{1: 4, 2: 4}","[21, 34, 52]"
2025-06-07,2025146,42,198,27,12,15,44.44,4,0,"{2: 1, 3: 2, 1: 3, 4: 1}",
2025-06-07,2025146,43,346,37,20,17,54.05,4,2,"{1: 3, 3: 2, 4: 1, 2: 2}",
2025-06-07,2025146,44,293,32,17,15,53.12,3,0,"{1: 6, 2: 3, 3: 1}",
2025-06-07,2025146,45,660,48,36,12,75.0,2,0,"{2: 2, 1: 8}",
2025-06-07,2025146,46,550,40,30,10,75.0,2,0,"{1: 8, 2: 1}",
2025-06-07,2025146,47,365,38,21,17,55.26,3,1,"{1: 6, 2: 4, 3: 1}",
2025-06-07,2025146,48,321,39,19,20,48.72,7,0,"{1: 4, 7: 1, 4: 1, 2: 1, 3: 1}",
2025-06-07,2025146,49,406,49,24,25,48.98,5,0,"{4: 1, 1: 10, 5: 1, 2: 3}",
2025-06-07,2025146,50,441,42,25,17,59.52,4,0,"{1: 7, 4: 2, 2: 1}",
2025-06-07,2025146,51,284,26,16,10,61.54,3,0,"{2: 1, 1: 5, 3: 1}",
2025-06-07,2025146,52,277,19,15,4,78.95,2,1,"{1: 2, 2: 1}",
2025-06-07,2025146,53,229,22,13,9,59.09,3,1,"{1: 2, 2: 2, 3: 1}",
2025-06-07,2025146,54,229,22,13,9,59.09,3,0,"{2: 1, 1: 4, 3: 1}",
2025-06-07,2025146,55,214,19,12,7,63.16,2,1,"{2: 2, 1: 3}",
2025-06-07,2025146,56,83,11,5,6,45.45,3,0,"{1: 1, 2: 1, 3: 1}","[34, 52, 65]"
2025-06-07,2025146,57,244,25,14,11,56.0,4,0,"{1: 5, 4: 1, 2: 1}",
2025-06-07,2025146,58,286,25,16,9,64.0,6,0,"{1: 3, 6: 1}",
2025-06-07,2025146,59,138,15,8,7,53.33,4,0,"{1: 3, 4: 1}",
2025-06-07,2025146,60,210,21,12,9,57.14,3,1,"{1: 4, 2: 1, 3: 1}",
2025-06-07,2025146,61,294,21,16,5,76.19,1,1,{1: 5},
2025-06-07,2025146,62,110,8,6,2,75.0,2,0,{2: 1},
2025-06-07,2025146,63,220,16,12,4,75.0,2,0,"{1: 2, 2: 1}",
2025-06-07,2025146,64,235,19,13,6,68.42,2,1,"{1: 4, 2: 1}",
2025-06-07,2025146,65,243,15,13,2,86.67,1,0,{1: 2},
2025-06-07,2025146,66,277,19,15,4,78.95,1,0,{1: 4},
2025-06-07,2025146,67,224,14,12,2,85.71,1,0,{1: 2},
2025-06-07,2025146,68,203,14,11,3,78.57,2,1,"{2: 1, 1: 1}",
2025-06-07,2025146,69,222,15,12,3,80.0,2,0,"{2: 1, 1: 1}",
2025-06-07,2025146,70,144,12,8,4,66.67,1,1,{1: 4},
2025-06-07,2025146,71,106,10,6,4,60.0,4,0,{4: 1},
2025-06-07,2025146,72,214,19,12,7,63.16,3,0,"{3: 1, 1: 2, 2: 1}",
2025-06-07,2025146,73,197,17,11,6,64.71,2,0,"{2: 2, 1: 2}",
2025-06-07,2025146,74,163,13,9,4,69.23,2,0,"{1: 2, 2: 1}",
2025-06-07,2025146,75,144,12,8,4,66.67,2,0,"{1: 2, 2: 1}","[5, 65, 68]"
2025-06-07,2025146,76,148,10,8,2,80.0,2,0,{2: 1},
2025-06-07,2025146,77,85,10,5,5,50.0,3,0,"{3: 1, 2: 1}","[5, 65, 68]"
2025-06-07,2025146,78,106,10,6,4,60.0,2,1,"{2: 1, 1: 2}",
2025-06-07,2025146,79,125,11,7,4,63.64,2,0,"{1: 2, 2: 1}",
2025-06-07,2025146,80,140,14,8,6,57.14,5,0,"{1: 1, 5: 1}",
2025-06-07,2025146,81,165,12,9,3,75.0,1,0,{1: 3},
2025-06-07,2025146,82,87,9,5,4,55.56,3,0,"{1: 1, 3: 1}",
2025-06-07,2025146,83,100,13,6,7,46.15,4,1,"{2: 1, 4: 1, 1: 1}",
2025-06-07,2025146,84,83,11,5,6,45.45,4,1,"{1: 2, 4: 1}",
2025-06-07,2025146,85,125,11,7,4,63.64,1,0,{1: 4},
2025-06-07,2025146,86,66,9,4,5,44.44,3,0,"{1: 2, 3: 1}",
2025-06-07,2025146,87,134,17,8,9,47.06,4,1,"{4: 1, 1: 3, 2: 1}",
2025-06-07,2025146,88,87,9,5,4,55.56,2,0,"{1: 2, 2: 1}",
2025-06-07,2025146,89,70,7,4,3,57.14,2,0,"{1: 1, 2: 1}",
2025-06-07,2025146,90,140,14,8,6,57.14,1,1,{1: 6},
2025-06-07,2025146,91,68,8,4,4,50.0,3,0,"{1: 1, 3: 1}",
2025-06-07,2025146,92,108,9,6,3,66.67,2,2,"{1: 1, 2: 1}",
2025-06-07,2025146,93,123,12,7,5,58.33,2,0,"{2: 2, 1: 1}",
2025-06-07,2025146,94,138,15,8,7,53.33,3,0,"{3: 1, 2: 1, 1: 2}",
2025-06-07,2025146,95,182,14,10,4,71.43,1,1,{1: 4},
2025-06-07,2025146,96,32,5,2,3,40.0,2,0,"{2: 1, 1: 1}",
2025-06-07,2025146,97,180,15,10,5,66.67,1,1,{1: 5},
2025-06-07,2025146,98,199,16,11,5,68.75,2,2,"{2: 2, 1: 1}",
2025-06-07,2025146,99,218,17,12,5,70.59,3,0,"{1: 2, 3: 1}",
2025-06-07,2025146,100,180,15,10,5,66.67,2,0,"{1: 3, 2: 1}",
2025-06-07,2025146,101,193,19,11,8,57.89,4,0,"{1: 2, 4: 1, 2: 1}",
2025-06-07,2025146,102,140,14,8,6,57.14,3,0,"{1: 1, 3: 1, 2: 1}",
2025-06-07,2025146,103,121,13,7,6,53.85,2,0,"{1: 2, 2: 2}",
2025-06-07,2025146,104,140,14,8,6,57.14,3,1,"{3: 1, 2: 1, 1: 1}",
2025-06-07,2025146,105,144,12,8,4,66.67,1,0,{1: 4},
2025-06-07,2025146,106,55,4,3,1,75.0,1,0,{1: 1},
2025-06-07,2025146,107,150,9,8,1,88.89,1,0,{1: 1},
2025-06-07,2025146,108,91,7,5,2,71.43,1,0,{1: 2},
2025-06-07,2025146,109,169,10,9,1,90.0,1,0,{1: 1},
2025-06-07,2025146,110,85,10,5,5,50.0,2,1,"{2: 2, 1: 1}",
2025-06-07,2025146,111,165,12,9,3,75.0,2,2,"{1: 1, 2: 1}",
2025-06-07,2025146,112,45,9,3,6,33.33,3,2,"{3: 1, 1: 1, 2: 1}",
2025-06-07,2025146,113,60,12,4,8,33.33,3,0,"{3: 1, 2: 2, 1: 1}",
2025-06-07,2025146,114,113,17,7,10,41.18,7,0,"{7: 1, 3: 1}",
2025-06-07,2025146,115,62,11,4,7,36.36,3,0,"{2: 2, 3: 1}",
2025-06-07,2025146,116,106,10,6,4,60.0,3,0,"{1: 1, 3: 1}",
2025-06-07,2025146,117,150,9,8,1,88.89,1,0,{1: 1},
2025-06-07,2025146,118,70,7,4,3,57.14,2,0,"{1: 1, 2: 1}",
2025-06-07,2025146,119,119,14,7,7,50.0,2,0,"{2: 2, 1: 3}",
2025-06-07,2025146,120,136,16,8,8,50.0,4,0,"{1: 4, 4: 1}",
2025-06-07,2025146,121,123,12,7,5,58.33,3,0,"{3: 1, 2: 1}",
2025-06-07,2025146,122,64,10,4,6,40.0,3,0,"{2: 1, 1: 1, 3: 1}",
2025-06-07,2025146,123,115,16,7,9,43.75,4,0,"{2: 2, 4: 1, 1: 1}",
2025-06-07,2025146,124,102,12,6,6,50.0,3,0,"{1: 1, 3: 1, 2: 1}",
2025-06-07,2025146,125,125,11,7,4,63.64,3,0,"{3: 1, 1: 1}",
2025-06-07,2025146,126,66,9,4,5,44.44,5,0,{5: 1},
2025-06-07,2025146,127,123,12,7,5,58.33,2,0,"{1: 3, 2: 1}",
2025-06-07,2025146,128,123,12,7,5,58.33,2,0,"{1: 3, 2: 1}",
2025-06-07,2025146,129,144,12,8,4,66.67,2,0,"{1: 2, 2: 1}",
2025-06-07,2025146,130,47,8,3,5,37.5,2,0,"{2: 2, 1: 1}",
2025-06-07,2025146,131,39,12,3,9,25.0,4,1,"{4: 2, 1: 1}",
2025-06-07,2025146,132,172,19,10,9,52.63,4,0,"{4: 1, 2: 2, 1: 1}",
2025-06-07,2025146,133,64,10,4,6,40.0,4,0,"{4: 1, 2: 1}",
2025-06-07,2025146,134,43,10,3,7,30.0,3,1,"{3: 2, 1: 1}",
2025-06-07,2025146,135,102,12,6,6,50.0,2,1,"{1: 2, 2: 2}",
2025-06-07,2025146,136,74,5,4,1,80.0,1,0,{1: 1},
2025-06-07,2025146,137,30,6,2,4,33.33,3,1,"{3: 1, 1: 1}",
2025-06-07,2025146,138,131,8,7,1,87.5,1,0,{1: 1},
2025-06-07,2025146,139,165,12,9,3,75.0,1,1,{1: 3},
2025-06-07,2025146,140,146,11,8,3,72.73,2,0,"{1: 1, 2: 1}",
2025-06-07,2025146,141,144,12,8,4,66.67,2,0,"{2: 1, 1: 2}",
2025-06-07,2025146,142,165,12,9,3,75.0,2,0,"{2: 1, 1: 1}",
2025-06-07,2025146,143,127,10,7,3,70.0,1,0,{1: 3},
2025-06-07,2025146,144,110,8,6,2,75.0,1,0,{1: 2},
2025-06-07,2025146,145,95,5,5,0,100.0,0,0,{},
2025-06-07,2025146,146,19,1,1,0,100.0,0,0,{},
2025-06-07,2025146,147,36,3,2,1,66.67,1,1,{1: 1},
2025-06-07,2025146,148,53,5,3,2,60.0,1,0,{1: 2},
2025-06-07,2025146,149,70,7,4,3,57.14,2,0,"{2: 1, 1: 1}",
2025-06-07,2025146,150,95,5,5,0,100.0,0,0,{},
2025-06-07,2025146,151,17,2,1,1,50.0,1,0,{1: 1},
